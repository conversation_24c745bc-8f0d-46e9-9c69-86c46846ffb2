/* Add padding to footer for spacing */
.footer {
  padding-top: 2.5rem;
}
/* Hero Section Down Arrow Button Styling */
.scroll-arrow {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.2rem;
  color: var(--secondary-color);
  transition: color 0.2s, transform 0.2s;
  margin: 0 auto;
  outline: none;
  box-shadow: none;
}
.scroll-arrow:hover,
.scroll-arrow:focus {
  color: var(--primary-color);
  transform: translateY(6px) scale(1.15);
}
/* Center and align hero CTA buttons */
.hero-cta {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--space-lg);
}

.hero-cta-buttons {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
}
/* Modern Design System Variables */
:root {
  /* Enhanced Primary Colors - Gambit Solutions Brand Colors */
  --primary-gradient: linear-gradient(135deg, #6bb6c6 0%, #5e768f 50%, #4a5f7a 100%);
  --secondary-gradient: linear-gradient(135deg, #5e768f 0%, #6bb6c6 50%, #7bc4d4 100%);
  --accent-gradient: linear-gradient(135deg, #6bb6c6 0%, #5e768f 50%, #f8f8ff 100%);
  --hero-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #5e768f 50%, #6bb6c6 75%, #7bc4d4 100%);
  --glass-gradient: linear-gradient(135deg, rgba(248,248,255,0.1) 0%, rgba(248,248,255,0.05) 100%);

  /* Solid Colors - Gambit Solutions Brand Palette */
  --primary-color: #6bb6c6;
  --primary-dark: #5a9fb0;
  --primary-light: #7bc4d4;
  --secondary-color: #5e768f;
  --secondary-dark: #4a5f7a;
  --secondary-light: #6e8399;
  --accent-color: #f8f8ff;
  --accent-dark: #e6e6fa;
  --accent-light: #ffffff;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  /* Enhanced Neutral Colors - Ghost White Base */
  --white: #f8f8ff;
  --ghost-white: #f8f8ff;
  --gray-50: #f5f7fa;
  --gray-100: #eef2f7;
  --gray-200: #dde4ed;
  --gray-300: #c4d1e0;
  --gray-400: #8fa3b8;
  --gray-500: #5e768f;
  --gray-600: #4a5f7a;
  --gray-700: #3a4d5f;
  --gray-800: #2c3e50;
  --gray-900: #1a252f;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-heading: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Enhanced Shadows with color tints */
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-colored: 0 10px 25px -5px rgba(107, 182, 198, 0.1), 0 4px 6px -2px rgba(107, 182, 198, 0.05);
  --shadow-glow: 0 0 20px rgba(107, 182, 198, 0.3);

  /* Enhanced Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-bg-strong: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20px);
  --glass-backdrop-strong: blur(40px);

  /* Enhanced Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--gray-800);
  background: var(--white);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.1;
  color: var(--gray-900);
  margin-bottom: var(--space-md);
  letter-spacing: -0.025em;
}

h1 {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 800;
  line-height: 1.05;
  letter-spacing: -0.04em;
}
h2 {
  font-size: clamp(2rem, 4.5vw, 3rem);
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.03em;
}
h3 {
  font-size: clamp(1.5rem, 3.5vw, 2.25rem);
  font-weight: 600;
  line-height: 1.15;
  letter-spacing: -0.02em;
}
h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin-bottom: var(--space-md);
  color: var(--gray-600);
  line-height: 1.75;
  font-size: 1.1rem;
  font-weight: 400;
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast);
}

a:hover {
  color: var(--secondary-color);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }
}

/* Section Styles */
.section {
  padding: var(--space-4xl) 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.section-title {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-md);
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

.section-divider {
  width: 80px;
  height: 4px;
  background: var(--accent-gradient);
  margin: var(--space-lg) auto;
  border-radius: var(--radius-sm);
}


/* Enhanced Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--glass-bg-strong);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  z-index: 1000;
  transition: all var(--transition-normal);
  padding: var(--space-md) 0;
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-lg);
  padding: var(--space-sm) 0;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-family: var(--font-heading);
  font-weight: 800;
  font-size: 1.5rem;
  color: var(--white);
  text-decoration: none;
  transition: all var(--transition-normal);
}

.navbar.scrolled .logo {
  color: var(--primary-color);
}

.logo-icon {
  font-size: 1.8rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-text {
  letter-spacing: -0.02em;
}

.logo-image {
  height: 2.5rem;
  width: auto;
  transition: all var(--transition-normal);
  filter: brightness(1.1);
}

.navbar.scrolled .logo-image {
  filter: brightness(0.9) hue-rotate(10deg);
}

.nav-menu {
  display: flex;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: var(--space-lg);
  margin: 0;
  padding: 0;
}

.nav-link {
  color: var(--white);
  font-weight: 600;
  font-size: 1rem;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  position: relative;
  transition: all var(--transition-normal);
}

.navbar.scrolled .nav-link {
  color: var(--gray-700);
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: all var(--transition-slow);
  transform: translateX(-50%);
  border-radius: 1px;
  box-shadow: 0 0 0 rgba(107, 182, 198, 0);
}

.nav-link:hover::before,
.nav-link.active::before {
  width: 100%;
  box-shadow: 0 0 8px rgba(107, 182, 198, 0.4), 0 0 16px rgba(107, 182, 198, 0.2);
  animation: glowPulse 2s ease-in-out infinite;
}

.nav-link.active::before {
  box-shadow: 0 0 12px rgba(107, 182, 198, 0.6), 0 0 24px rgba(107, 182, 198, 0.3);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-light);
}

.navbar.scrolled .nav-link:hover,
.navbar.scrolled .nav-link.active {
  color: var(--primary-color);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-sm);
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
  transition: all var(--transition-normal);
}

.navbar.scrolled .mobile-menu-toggle span {
  background: var(--gray-700);
}


/* Enhanced Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  background: var(--hero-gradient);
  color: var(--white);
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 80%; animation-delay: 2s; }
.particle:nth-child(3) { top: 80%; left: 40%; animation-delay: 4s; }
.particle:nth-child(4) { top: 40%; left: 70%; animation-delay: 1s; }
.particle:nth-child(5) { top: 30%; left: 10%; animation-delay: 3s; }

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.hero-logo-container {
  position: relative;
  display: inline-block;
  margin-bottom: var(--space-xl);
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(107, 182, 198, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
}

.hero-logo {
  position: relative;
  z-index: 1;
  max-width: 150px;
  filter: drop-shadow(0 8px 32px rgba(107, 182, 198, 0.4));
}

.hero-title {
  margin-bottom: var(--space-xl);
}

.title-main {
  display: block;
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  margin-bottom: var(--space-sm);
}

.title-subtitle {
  display: block;
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  font-weight: 400;
  color: var(--gray-300);
  letter-spacing: 0.05em;
}

.founder-info {
  margin-bottom: var(--space-xl);
  padding: var(--space-md) var(--space-xl);
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  display: inline-block;
}

.founder-name {
  display: block;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--accent-light);
  margin-bottom: var(--space-xs);
}

.founder-title {
  display: block;
  font-size: 1rem;
  color: var(--gray-300);
  font-weight: 400;
}

.hero-description {
  max-width: 700px;
  margin: 0 auto var(--space-xl);
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--gray-200);
  font-weight: 300;
}

.hero-cta {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: var(--space-2xl);
}
/* Enhanced Button Styles */
.btn-primary {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  background: var(--primary-gradient);
  color: var(--white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  box-shadow: var(--shadow-colored);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  background: rgba(94, 118, 143, 0.9);
  color: var(--white);
  border: 2px solid rgba(107, 182, 198, 0.5);
  border-radius: var(--radius-lg);
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  transition: all var(--transition-normal);
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(107, 182, 198, 0.4);
}
/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: var(--space-xl);
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.scroll-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid var(--white);
  border-radius: 50%;
  color: var(--white);
  animation: bounce 2s infinite;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.scroll-arrow:hover {
  background: var(--white);
  color: var(--primary-color);
  transform: scale(1.1);
}

/* Enhanced Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(107, 182, 198, 0.4), 0 0 16px rgba(107, 182, 198, 0.2);
  }
  50% {
    box-shadow: 0 0 12px rgba(107, 182, 198, 0.6), 0 0 24px rgba(107, 182, 198, 0.3);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-slide-up {
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.8s ease-out forwards;
}

.animate-slide-up.delay-1 { animation-delay: 0.2s; }
.animate-slide-up.delay-2 { animation-delay: 0.4s; }
.animate-slide-up.delay-3 { animation-delay: 0.6s; }

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-in {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}




/* Section Styling */
.section {
  padding: 4rem 2rem;
}
.light-bg {
  background-color: var(--light-grey);
}
.white-bg {
  background-color: var(--white);
}
.animated-fadein {
  opacity: 0;
  animation: fadeIn 1.2s ease 0.2s forwards;
}
.animated-slidein {
  opacity: 0;
  transform: translateY(40px);
  animation: slideIn 1.1s cubic-bezier(.4,1.4,.6,1) 0.3s forwards;
}
.animated-pop {
  opacity: 0;
  transform: scale(0.96);
  animation: popIn 1s cubic-bezier(.4,1.4,.6,1) 0.5s forwards;
}
@keyframes fadeIn {
  to { opacity: 1; }
}
@keyframes slideIn {
  to { opacity: 1; transform: none; }
}
@keyframes popIn {
  60% { opacity: 1; transform: scale(1.04); }
  100% { opacity: 1; transform: scale(1); }
}


/* Enhanced Projects Section */
.projects-section {
  background: linear-gradient(180deg, var(--gray-50) 0%, var(--white) 100%);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.project-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  position: relative;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-color);
}

.project-card.featured-project {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  max-width: none;
}

.project-image {
  position: relative;
  overflow: hidden;
  aspect-ratio: 16/10;
}

.featured-project .project-image {
  aspect-ratio: auto;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.8) 0%, rgba(139, 92, 246, 0.8) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  justify-content: center;
}

.tech-tag {
  background: var(--white);
  color: var(--primary-color);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
}

.project-content {
  padding: var(--space-xl);
}

.featured-project .project-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-md);
  gap: var(--space-md);
}

.project-header h3 {
  margin: 0;
  font-size: 1.5rem;
  line-height: 1.3;
  color: var(--gray-900);
}

.project-status {
  flex-shrink: 0;
}

.status-badge {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.completed {
  background: var(--success-color);
  color: var(--white);
}

.project-tagline {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--space-md);
  font-style: italic;
}

.project-description {
  color: var(--gray-600);
  line-height: 1.7;
  margin-bottom: var(--space-lg);
}

.project-features ul {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-lg) 0;
}

.project-features li {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-sm);
  color: var(--gray-700);
}

.project-features i {
  color: var(--success-color);
  font-size: 0.875rem;
}

.project-actions {
  margin-top: auto;
}
/* Enhanced Upcoming Projects Section */
.upcoming-section {
  background: var(--gray-900);
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.upcoming-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at top, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.upcoming-section .section-title {
  color: var(--white);
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.upcoming-section .section-subtitle {
  color: var(--gray-300);
}

.upcoming-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.upcoming-card {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.upcoming-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.upcoming-card:hover {
  transform: translateY(-4px);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-xl);
}

.upcoming-header {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.upcoming-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-gradient);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white);
  flex-shrink: 0;
}

.upcoming-meta h3 {
  margin: 0 0 var(--space-xs) 0;
  color: var(--white);
  font-size: 1.5rem;
}

.project-count {
  color: var(--primary-light);
  font-size: 0.9rem;
  font-weight: 500;
}

.upcoming-description {
  color: var(--gray-300);
  line-height: 1.7;
  margin-bottom: var(--space-xl);
}

.progress-section {
  margin-bottom: var(--space-xl);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.progress-label {
  color: var(--gray-400);
  font-size: 0.9rem;
  font-weight: 500;
}

.progress-percentage {
  color: var(--primary-light);
  font-weight: 600;
}

.progress-bar {
  height: 8px;
  background: var(--gray-700);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: var(--radius-sm);
  transition: width var(--transition-slow);
}

.showcase-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  margin-bottom: var(--space-xl);
}

.showcase-tag {
  background: var(--gray-800);
  color: var(--gray-300);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  border: 1px solid var(--gray-700);
}

.timeline {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-xl);
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gray-700);
  z-index: 1;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  position: relative;
  z-index: 2;
  flex: 1;
  text-align: center;
}

.timeline-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--gray-700);
  border: 3px solid var(--gray-900);
  transition: all var(--transition-normal);
}

.timeline-item.completed .timeline-dot {
  background: var(--success-color);
}

.timeline-item.active .timeline-dot {
  background: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.3);
}

.timeline-item span {
  font-size: 0.8rem;
  color: var(--gray-400);
  font-weight: 500;
}

.timeline-item.completed span,
.timeline-item.active span {
  color: var(--white);
}

.upcoming-footer {
  margin-top: auto;
}

.status-badge.in-development {
  background: var(--primary-color);
  color: var(--white);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.status-badge.planning {
  background: var(--warning-color);
  color: var(--white);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}


.project-card {
  margin-bottom: 4rem;
}

.project-card img {
  width: 100%;
  max-height: 400px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.project-card h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.project-card ul {
  margin: 0.5rem 0 1rem 1.5rem;
  list-style: disc;
}

.project-card .tagline {
  font-style: italic;
  color: #666;
}

hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 3rem 0;
}



/* Enhanced Contact Section */
.contact-section {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  position: relative;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  margin-top: var(--space-3xl);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.contact-card {
  background: var(--white);
  padding: var(--space-xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  text-align: center;
}

.contact-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.contact-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-md);
  font-size: 1.5rem;
  color: var(--white);
}

.contact-card h3 {
  margin: 0 0 var(--space-sm) 0;
  color: var(--gray-900);
  font-size: 1.25rem;
}

.contact-card p {
  color: var(--gray-600);
  margin-bottom: var(--space-md);
  font-size: 0.95rem;
}

.contact-link {
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  transition: color var(--transition-fast);
}

.contact-link:hover {
  color: var(--primary-dark);
}

.contact-form {
  background: var(--white);
  padding: var(--space-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
}

.contact-form-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-sm);
  font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
  padding: var(--space-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
  font-family: var(--font-primary);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.contact-container {
  max-width: 800px;
  margin: auto;
  padding-top: 2rem;
}

.contact-info ul.contact-list {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
  font-size: 1.1rem;
}

.contact-list li {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-list i {
  color: var(--primary-blue);
  min-width: 20px;
}

.contact-form form {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-form label {
  font-weight: bold;
  display: block;
  margin-bottom: 0.5rem;
}

.contact-form input,
.contact-form textarea {
  padding: 0.9rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  width: 100%;
}

.contact-form .btn-primary {
  width: fit-content;
}

.form-note {
  font-size: 0.9rem;
  color: #666;
  margin-top: 1rem;
  text-align: center;
}



/* Enhanced Footer */
.footer {
  background: var(--gray-900);
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at top, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--space-2xl);
  margin-bottom: var(--space-2xl);
  position: relative;
  z-index: 1;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
}

.footer-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  font-family: var(--font-heading);
  font-weight: 800;
  font-size: 1.5rem;
  color: var(--white);
}

.footer-logo-image {
  height: 4rem;
  width: auto;
  filter: brightness(1.1);
}

.footer-tagline {
  color: var(--gray-400);
  font-size: 0.95rem;
  margin: 0;
}

.footer-links {
  display: flex;
  gap: var(--space-2xl);
}

.footer-section h4 {
  color: var(--white);
  font-size: 1.1rem;
  margin-bottom: var(--space-md);
  font-weight: 600;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: var(--space-sm);
}

.footer-section a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
  font-size: 0.95rem;
}

.footer-section a:hover {
  color: var(--primary-light);
}

.footer-social {
  display: flex;
  gap: var(--space-md);
  justify-content: flex-end;
}

.social-link {
  width: 44px;
  height: 44px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  text-decoration: none;
  transition: all var(--transition-normal);
  font-size: 1.2rem;
}

.social-link:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding-top: var(--space-lg);
  text-align: center;
  position: relative;
  z-index: 1;
}

.footer-bottom p {
  color: var(--gray-400);
  font-size: 0.9rem;
  margin-bottom: var(--space-sm);
}

.footer-credit {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
}

.footer-credit i {
  color: var(--error-color);
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}


.about-container {
  max-width: 1000px;
  margin: auto;
  padding-top: 2rem;
}

.about-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-top: 2rem;
}

.about-text {
  flex: 1 1 60%;
}

.about-photo {
  flex: 1 1 35%;
  text-align: center;
}

.about-photo img {
  max-width: 100%;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.about-photo blockquote {
  font-style: italic;
  color: #555;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.advantage-card {
  background: var(--light-grey);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
}

.advantage-card i {
  font-size: 1.5rem;
  color: var(--primary-blue);
  margin-bottom: 0.5rem;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.value-box {
  background: #f0f0f0;
  padding: 1.2rem;
  border-left: 4px solid var(--primary-blue);
  border-radius: 6px;
}

.about-cta {
  text-align: center;
  margin-top: 4rem;
}

.cta-buttons {
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 1024px) {
  .container {
    padding: 0 var(--space-lg);
  }

  .hero-content {
    padding: 0 var(--space-md);
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .project-card.featured-project {
    grid-template-columns: 1fr;
  }

  .upcoming-grid {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }

  .footer-social {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 999;
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-links {
    flex-direction: column;
    padding: var(--space-xl);
    gap: var(--space-md);
  }

  .nav-link {
    color: var(--gray-700);
    padding: var(--space-md);
    border-radius: var(--radius-md);
    text-align: center;
  }

  .hero {
    min-height: 90vh;
    padding-top: 80px;
  }

  .hero-content {
    padding: 0 var(--space-md);
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .section {
    padding: var(--space-3xl) 0;
  }

  .section-header {
    margin-bottom: var(--space-2xl);
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .project-card {
    margin: 0;
  }

  .upcoming-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .upcoming-card {
    padding: var(--space-xl);
  }

  .upcoming-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }

  .timeline {
    flex-direction: column;
    gap: var(--space-md);
  }

  .timeline::before {
    display: none;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .contact-form {
    padding: var(--space-xl);
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    text-align: center;
  }

  .footer-links {
    flex-direction: column;
    gap: var(--space-lg);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-md);
  }

  .hero-logo {
    max-width: 120px;
  }

  .founder-info {
    padding: var(--space-sm) var(--space-lg);
  }

  .upcoming-card {
    padding: var(--space-lg);
  }

  .contact-card {
    padding: var(--space-lg);
  }

  .contact-form {
    padding: var(--space-lg);
  }

  .showcase-tags {
    justify-content: center;
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }

.hidden { display: none; }
.visible { display: block; }

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

button:focus,
a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Smooth scrolling for all browsers */
html {
  scroll-behavior: smooth;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}